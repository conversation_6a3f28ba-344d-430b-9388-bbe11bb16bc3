# 钉钉物料申请流程扩展字段处理功能说明

## 功能概述

在钉钉物料申请流程完成（`finish` 且 `agree`）时，自动处理以下功能：

1. **提取钉钉表单字段**：从"申请物料明细"中提取特定字段并存储到Part的extensionContent属性中
2. **处理抗辐照指标附件**：创建Document对象并建立与Part的REFERENCE_KFZ关联关系

## 实现位置

### 主要修改文件

1. **DingTalkCallListen.java** (第758行)
   - 在 `if ("finish".equals(type) && "agree".equals(result))` 条件中添加了调用新功能的代码

2. **DingTalkHelper.java**
   - 新增了 `processPartExtensionAndRadiationDocuments` 方法及相关辅助方法

## 功能详细说明

### 1. 扩展字段提取和存储

从钉钉表单"申请物料明细"中提取以下字段：
- **是否辐照敏感**：存储为 `isRadiationSensitive`
- **元器件详细标准号**：存储为 `componentDetailStandard`  
- **有效贮存期（年）**：存储为 `effectiveStorageYears`

这些字段会被存储到对应Part对象的 `extensionContent` 属性中，同时还会记录流程相关信息：
- `processInstanceId`：钉钉流程实例ID
- `processBusinessId`：审批单号
- `updateTime`：更新时间戳

### 2. 抗辐照指标文档处理

对于"抗辐照指标"字段中的附件：

1. **下载附件**：从钉钉获取附件并下载到临时目录
2. **上传到MinIO**：将附件上传到MinIO存储系统
3. **创建Document对象**：
   - 文档名称：去除扩展名的文件名
   - 文档类型：`JWIGeneralDocument`
   - 位置：与关联的Part在同一位置
   - 分类：抗辐照指标分类（`cn_yh_kfzz`）
   - 状态：已发布（Released）
4. **建立关联关系**：
   - 关系类型：`REFERENCE_KFZ`
   - 约束：`MM`（多对多）
   - 方向：`setForward(true)`
   - 从Part到Document的关联

## 核心方法说明

### processPartExtensionAndRadiationDocuments
主入口方法，负责：
- 获取钉钉流程表单数据
- 遍历"申请物料明细"中的每一行
- 调用字段更新和文档处理方法

### updatePartExtensionContent
更新Part的extensionContent属性：
- 检出Part对象
- 更新扩展字段
- 检入Part对象

### processRadiationResistanceDocuments
处理抗辐照指标文档：
- 解析附件JSON数组
- 下载每个附件
- 创建Document对象
- 建立关联关系

### createRadiationResistanceDocument
创建抗辐照指标文档：
- 设置文档基本信息
- 配置位置和分类
- 上传文件到MinIO
- 设置状态为已发布

### createReferenceKfzRelation
创建REFERENCE_KFZ关联关系：
- 配置关联操作参数
- 执行批量关联操作

## 错误处理

- 所有方法都包含完整的异常处理
- 记录详细的日志信息
- 在DingTalkCallListen中，处理异常时仅记录错误，不中断流程

## 使用场景

当钉钉物料申请流程审批通过时，系统会自动：
1. 将表单中的关键字段保存到Part对象中，便于后续查询和使用
2. 将抗辐照指标相关文档归档到PDM系统中，并与对应的Part建立关联
3. 确保数据的完整性和可追溯性

## 注意事项

1. 该功能仅在流程状态为"finish"且结果为"agree"时触发
2. 需要确保钉钉表单中包含所需的字段名称
3. 抗辐照指标附件会被自动上传到MinIO并创建对应的Document对象
4. 所有操作都会记录详细的日志，便于问题排查
